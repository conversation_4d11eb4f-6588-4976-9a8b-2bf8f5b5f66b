import type { Message } from '$lib/types/customer';
import { CustomerService } from '$lib/api/features/customer/customers.service';
import { conversationStore } from '$lib/stores/conversationStore';
import { get } from 'svelte/store';
import { getBackendUrl } from '$src/lib/config';

// Types for the mark-as-read service
export interface MarkAsReadContext {
    ticket?: {
        owner_id: number;
        status: string;
    } | null;
    loginUser: number;
    activeTab: string;
    access_token: string;
    platformId: number;
    customerId: number;
}

export interface MarkAsReadOptions {
    // Whether this is an automatic marking (vs manual user action)
    isAutomatic?: boolean;
    // Whether to validate permissions before marking
    validatePermissions?: boolean;
    // Whether to update stores after successful API call
    updateStores?: boolean;
    // Whether to dispatch events after marking
    dispatchEvents?: boolean;
    // Custom validation function
    customValidation?: (context: MarkAsReadContext) => boolean;
}

export interface MarkAsReadResult {
    success: boolean;
    markedCount: number;
    messageIds: number[];
    error?: string;
    skipped?: boolean;
    skipReason?: string;
}

export interface AutoMarkValidation {
    canMark: boolean;
    reason?: string;
    shouldRetryLater?: boolean;
}

/**
 * Centralized service for handling all mark-as-read operations
 * Consolidates logic from ConversationView and ChannelMessages components
 */
export class MarkAsReadService {
    private customerService: CustomerService;

    constructor() {
        this.customerService = new CustomerService();
    }

    /**
     * Mark specific messages as read
     */
    async markMessages(
        messageIds: number[],
        context: MarkAsReadContext,
        options: MarkAsReadOptions = {}
    ): Promise<MarkAsReadResult> {
        const {
            validatePermissions = true,
            updateStores = true,
            dispatchEvents = false,
            customValidation
        } = options;

        try {
            console.log('MarkAsReadService: markMessages called', {
                messageIds,
                messageIdsTypes: messageIds.map(id => typeof id),
                context: {
                    customerId: context.customerId,
                    platformId: context.platformId,
                    activeTab: context.activeTab,
                    ticketOwnerId: context.ticket?.owner_id,
                    loginUser: context.loginUser
                },
                options
            });

            // Validate inputs
            if (!messageIds || messageIds.length === 0) {
                return {
                    success: false,
                    markedCount: 0,
                    messageIds: [],
                    error: 'No message IDs provided',
                    skipped: true,
                    skipReason: 'No message IDs provided'
                };
            }

            if (!context.access_token) {
                return {
                    success: false,
                    markedCount: 0,
                    messageIds,
                    error: 'No access token available',
                    skipped: true,
                    skipReason: 'No access token available'
                };
            }

            // Validate message IDs are numbers
            const invalidIds = messageIds.filter(id => typeof id !== 'number' || isNaN(id));
            if (invalidIds.length > 0) {
                return {
                    success: false,
                    markedCount: 0,
                    messageIds,
                    error: `Invalid message IDs detected: ${invalidIds.join(', ')}`,
                    skipped: true,
                    skipReason: 'Invalid message IDs'
                };
            }

            // Permission validation
            if (validatePermissions) {
                const validation = this.validateAutoMarkPermissions(context);
                if (!validation.canMark) {
                    return {
                        success: false,
                        markedCount: 0,
                        messageIds,
                        error: validation.reason,
                        skipped: true,
                        skipReason: validation.reason
                    };
                }
            }

            // Custom validation
            if (customValidation && !customValidation(context)) {
                return {
                    success: false,
                    markedCount: 0,
                    messageIds,
                    error: 'Custom validation failed',
                    skipped: true,
                    skipReason: 'Custom validation failed'
                };
            }

            // Call API to mark messages as read
            console.log('MarkAsReadService: Calling API to mark messages as read...');
            const result = await this.customerService.markMessagesAsRead(
                context.customerId,
                context.platformId,
                messageIds,
                context.access_token
            );

            console.log('MarkAsReadService: API response:', {
                res_status: result.res_status,
                res_msg: result.res_msg,
                error_msg: result.error_msg,
                messageIds
            });

            if (result.res_status === 200) {
                console.log('MarkAsReadService: Successfully marked messages as read');

                // Update stores if requested
                if (updateStores) {
                    this.updateConversationStore(context.platformId, messageIds);
                }

                return {
                    success: true,
                    markedCount: messageIds.length,
                    messageIds
                };
            } else {
                return {
                    success: false,
                    markedCount: 0,
                    messageIds,
                    error: result.error_msg || 'API error marking messages as read'
                };
            }

        } catch (error) {
            console.error('MarkAsReadService: Exception in markMessages:', {
                error,
                messageIds,
                context
            });

            return {
                success: false,
                markedCount: 0,
                messageIds,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }

    /**
     * Mark all messages as read for a platform
     */
    async markAllMessages(
        context: MarkAsReadContext,
        options: MarkAsReadOptions = {}
    ): Promise<MarkAsReadResult> {
        const { validatePermissions = true } = options;

        try {
            console.log('MarkAsReadService: markAllMessages called', { context, options });

            if (!context.access_token) {
                return {
                    success: false,
                    markedCount: 0,
                    messageIds: [],
                    error: 'No access token available',
                    skipped: true,
                    skipReason: 'No access token available'
                };
            }

            // Permission validation
            if (validatePermissions) {
                const validation = this.validateAutoMarkPermissions(context);
                if (!validation.canMark) {
                    return {
                        success: false,
                        markedCount: 0,
                        messageIds: [],
                        error: validation.reason,
                        skipped: true,
                        skipReason: validation.reason
                    };
                }
            }

            const result = await this.customerService.markAllMessagesAsRead(
                context.customerId,
                context.platformId,
                context.access_token
            );

            if (result.res_status === 200) {
                console.log('MarkAsReadService: Successfully marked all messages as read');
                return {
                    success: true,
                    markedCount: 0, // API doesn't return count for mark all
                    messageIds: []
                };
            } else {
                return {
                    success: false,
                    markedCount: 0,
                    messageIds: [],
                    error: result.error_msg || 'API error marking all messages as read'
                };
            }

        } catch (error) {
            console.error('MarkAsReadService: Exception in markAllMessages:', { error, context });
            return {
                success: false,
                markedCount: 0,
                messageIds: [],
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }

    /**
     * Automatically mark unread customer messages when loading a conversation
     */
    async autoMarkOnLoad(
        context: MarkAsReadContext,
        options: MarkAsReadOptions = {}
    ): Promise<MarkAsReadResult> {
        try {
            console.log('MarkAsReadService: autoMarkOnLoad called', { context, options });

            // Check if we're in an assigned tab
            if (!(context.activeTab === 'my-assigned' || context.activeTab === 'others-assigned')) {
                return {
                    success: false,
                    markedCount: 0,
                    messageIds: [],
                    skipped: true,
                    skipReason: 'Not in assigned tab'
                };
            }

            // Validate permissions
            const validation = this.validateAutoMarkPermissions(context);
            if (!validation.canMark) {
                return {
                    success: false,
                    markedCount: 0,
                    messageIds: [],
                    skipped: validation.shouldRetryLater,
                    skipReason: validation.reason
                };
            }

            // Get unread customer messages from store
            const messages = get(conversationStore).messages.get(context.platformId) || [];
            const unreadMessageIds = messages
                .filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
                .map((msg: Message) => msg.id);

            console.log('MarkAsReadService: Found unread customer messages for auto-mark on load:', {
                totalMessages: messages.length,
                unreadCount: unreadMessageIds.length,
                unreadMessageIds
            });

            if (unreadMessageIds.length === 0) {
                return {
                    success: true,
                    markedCount: 0,
                    messageIds: [],
                    skipped: true,
                    skipReason: 'No unread customer messages found'
                };
            }

            // Mark the messages as read
            return await this.markMessages(unreadMessageIds, context, {
                ...options,
                validatePermissions: false // Already validated above
            });

        } catch (error) {
            console.error('MarkAsReadService: Exception in autoMarkOnLoad:', { error, context });
            return {
                success: false,
                markedCount: 0,
                messageIds: [],
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }

    /**
     * Automatically mark all messages as read when a WebSocket message is received if conditions are met
     */
    async autoMarkWebSocketMessage(
        message: Message,
        context: MarkAsReadContext,
        options: MarkAsReadOptions = {}
    ): Promise<MarkAsReadResult> {
        try {
            console.log('MarkAsReadService: autoMarkWebSocketMessage called - will mark all messages as read', {
                triggerMessageId: message.id,
                messageStatus: message.status,
                isCustomerMessage: !message.is_self,
                context: {
                    activeTab: context.activeTab,
                    ticketOwnerId: context.ticket?.owner_id,
                    loginUser: context.loginUser
                }
            });

            // Validate if this WebSocket message should trigger auto-marking all messages
            const shouldMark = this.shouldAutoMarkWebSocketMessage(message, context);
            if (!shouldMark.canMark) {
                return {
                    success: false,
                    markedCount: 0,
                    messageIds: [],
                    skipped: true,
                    skipReason: shouldMark.reason
                };
            }

            // Mark all messages as read instead of just the specific message
            return await this.markAllMessages(context, {
                ...options,
                validatePermissions: false // Already validated above
            });

        } catch (error) {
            console.error('MarkAsReadService: Exception in autoMarkWebSocketMessage:', { error, message, context });
            return {
                success: false,
                markedCount: 0,
                messageIds: [],
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }

    /**
     * Validate if automatic message marking is allowed based on context
     */
    validateAutoMarkPermissions(context: MarkAsReadContext): AutoMarkValidation {
        console.log('MarkAsReadService: Validating auto-mark permissions:', {
            hasTicket: !!context.ticket,
            ticketOwnerId: context.ticket?.owner_id,
            loginUser: context.loginUser,
            activeTab: context.activeTab,
            hasAccessToken: !!context.access_token
        });

        // Must have access token
        if (!context.access_token) {
            return {
                canMark: false,
                reason: 'No access token available'
            };
        }

        // Must be in an assigned tab for automatic marking
        if (!(context.activeTab === 'my-assigned' || context.activeTab === 'others-assigned')) {
            return {
                canMark: false,
                reason: 'Not in assigned tab'
            };
        }

        // Must have ticket data and user must own the ticket
        if (!context.ticket || !context.ticket.owner_id || context.ticket.owner.username !== context.loginUser) {
            return {
                canMark: false,
                reason: 'User does not own ticket or ticket data missing',
                shouldRetryLater: true
            };
        }

        return {
            canMark: true
        };
    }

    /**
     * Determine if a WebSocket message should be auto-marked as read
     */
    shouldAutoMarkWebSocketMessage(message: Message, context: MarkAsReadContext): AutoMarkValidation {
        console.log('MarkAsReadService: Evaluating shouldAutoMarkWebSocketMessage:', {
            messageId: message.id,
            messageStatus: message.status,
            isCustomerMessage: !message.is_self,
            isInAssignedTab: (context.activeTab === 'my-assigned' || context.activeTab === 'others-assigned'),
            hasTicket: !!context.ticket,
            ticketOwnerId: context.ticket?.owner_id,
            loginUser: context.loginUser,
            userOwnsTicket: context.ticket?.owner_id === context.loginUser,
            messageNotAlreadyRead: message.status !== 'READ',
            hasAccessToken: !!context.access_token
        });

        // Must be a customer message (not self)
        if (message.is_self) {
            return {
                canMark: false,
                reason: 'Message is from self, not auto-marking'
            };
        }

        // Message must not already be marked as READ
        if (message.status === 'READ') {
            return {
                canMark: false,
                reason: 'Message already marked as read'
            };
        }

        // Use the general validation for the rest
        return this.validateAutoMarkPermissions(context);
    }

    /**
     * Update conversation store after successful API call
     */
    private updateConversationStore(platformId: number, messageIds: number[]): void {
        try {
            console.log('MarkAsReadService: Updating conversation store:', {
                platformId,
                messageIds,
                storeHasPlatform: get(conversationStore).messages.has(platformId)
            });

            // Update message status in store
            messageIds.forEach((id) => {
                conversationStore.updateMessageStatus(platformId, id, 'READ');
            });

            console.log('MarkAsReadService: Store update completed');
        } catch (error) {
            console.error('MarkAsReadService: Error updating conversation store:', error);
        }
    }

    /**
     * Simple mark messages as read for components without full context (like ChannelMessages)
     * Uses direct API call without authentication or permission validation
     */
    async markMessagesSimple(
        customerId: number,
        platformId: number,
        messageIds: number[],
        updateStoreCallback?: (messageIds: number[]) => void
    ): Promise<MarkAsReadResult> {
        try {
            console.log('MarkAsReadService: markMessagesSimple called', {
                customerId,
                platformId,
                messageIds
            });

            if (!messageIds || messageIds.length === 0) {
                return {
                    success: false,
                    markedCount: 0,
                    messageIds: [],
                    error: 'No message IDs provided',
                    skipped: true,
                    skipReason: 'No message IDs provided'
                };
            }

            // Use direct fetch for simple cases (like ChannelMessages)
            const response = await fetch(
                `${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/mark-read/`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message_ids: messageIds
                    })
                }
            );

            if (response.ok) {
                const result = await response.json();
                console.log('MarkAsReadService: Simple mark messages successful');

                // Call the update callback if provided
                if (updateStoreCallback) {
                    updateStoreCallback(messageIds);
                }

                return {
                    success: true,
                    markedCount: messageIds.length,
                    messageIds
                };
            } else {
                const errorData = await response.json().catch(() => ({}));
                return {
                    success: false,
                    markedCount: 0,
                    messageIds,
                    error: errorData.error || 'API error marking messages as read'
                };
            }

        } catch (error) {
            console.error('MarkAsReadService: Exception in markMessagesSimple:', {
                error,
                messageIds,
                customerId,
                platformId
            });

            return {
                success: false,
                markedCount: 0,
                messageIds,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }

    /**
     * Create a context object from component state
     */
    static createContext(
        ticket: any,
        loginUser: number,
        activeTab: string,
        access_token: string,
        platformId: number,
        customerId: number
    ): MarkAsReadContext {
        return {
            ticket,
            loginUser,
            activeTab,
            access_token,
            platformId,
            customerId
        };
    }
}

// Export a singleton instance
export const markAsReadService = new MarkAsReadService();
